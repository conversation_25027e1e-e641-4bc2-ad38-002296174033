export interface ConversionRequest {
  url: string;
}

export interface ConversionResponse {
  success: boolean;
  message: string;
  downloadUrl?: string;
  filename?: string;
  error?: string;
}

export interface VideoInfo {
  title: string;
  duration: string;
  thumbnail: string;
  author: string;
}

export interface ConversionProgress {
  stage: 'validating' | 'downloading' | 'converting' | 'complete' | 'error';
  progress: number;
  message: string;
}

export interface ConversionError {
  code: string;
  message: string;
  details?: string;
}
