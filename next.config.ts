import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ['ytdl-core', 'fluent-ffmpeg', '@ffmpeg-installer/ffmpeg'],
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push('ytdl-core', 'fluent-ffmpeg', '@ffmpeg-installer/ffmpeg');
    }
    return config;
  },
  async headers() {
    return [
      {
        source: '/downloads/:path*',
        headers: [
          {
            key: 'Content-Disposition',
            value: 'attachment',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
