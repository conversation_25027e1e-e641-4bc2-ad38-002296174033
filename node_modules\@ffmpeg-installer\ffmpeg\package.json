{"name": "@ffmpeg-installer/ffmpeg", "version": "1.1.0", "main": "index.js", "scripts": {"lint": "jshint *.js", "preversion": "npm run lint", "types": "tsc", "preupload": "npm run types", "upload": "npm --userconfig=.npmrc publish --access public", "test": "tsd"}, "types": "types/index.d.ts", "keywords": ["ffmpeg", "binary", "installer", "audio", "sound"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "LGPL-2.1", "description": "Platform independent binary installer of FFmpeg for node projects", "optionalDependencies": {"@ffmpeg-installer/darwin-arm64": "4.1.5", "@ffmpeg-installer/darwin-x64": "4.1.0", "@ffmpeg-installer/linux-arm": "4.1.3", "@ffmpeg-installer/linux-arm64": "4.1.4", "@ffmpeg-installer/linux-ia32": "4.1.0", "@ffmpeg-installer/linux-x64": "4.1.0", "@ffmpeg-installer/win32-ia32": "4.1.0", "@ffmpeg-installer/win32-x64": "4.1.0"}, "devDependencies": {"jshint": "^2.9.3", "tsd": "^0.14.0", "typescript": "^4.2.3"}, "repository": {"type": "git", "url": "git+https://github.com/kribblo/node-ffmpeg-installer.git"}, "bugs": {"url": "https://github.com/kribblo/node-ffmpeg-installer/issues"}, "homepage": "https://github.com/kribblo/node-ffmpeg-installer#readme"}