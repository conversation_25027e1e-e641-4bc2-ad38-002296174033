import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "YouTube to MP3 Converter | Free Online Video to Audio Converter",
  description: "Convert YouTube videos to high-quality MP3 audio files for free. Fast, secure, and easy-to-use online converter with no registration required.",
  keywords: "YouTube to MP3, video converter, audio converter, download YouTube audio, free converter",
  authors: [{ name: "YouTube to MP3 Converter" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "YouTube to MP3 Converter",
    description: "Convert YouTube videos to high-quality MP3 audio files for free",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "YouTube to MP3 Converter",
    description: "Convert YouTube videos to high-quality MP3 audio files for free",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-white`}
      >
        {children}
      </body>
    </html>
  );
}
