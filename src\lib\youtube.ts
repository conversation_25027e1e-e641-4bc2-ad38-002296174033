import ytdl from 'ytdl-core';
import ffmpeg from 'fluent-ffmpeg';
import { promises as fs } from 'fs';
import path from 'path';
import { VideoInfo } from '@/types';
import { sanitizeFilename } from './validation';

// Try to set ffmpeg path, but handle errors gracefully
try {
  const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
  if (ffmpegPath) {
    ffmpeg.setFfmpegPath(ffmpegPath);
    console.log('FFmpeg path set to:', ffmpegPath);
  }
} catch (error) {
  console.warn('FFmpeg installer not available, using system ffmpeg if available:', error);
}

/**
 * Gets video information from YouTube URL
 * @param url - YouTube video URL
 * @returns Promise<VideoInfo>
 */
export async function getVideoInfo(url: string): Promise<VideoInfo> {
  try {
    const info = await ytdl.getInfo(url);
    const videoDetails = info.videoDetails;

    return {
      title: videoDetails.title,
      duration: formatDuration(parseInt(videoDetails.lengthSeconds)),
      thumbnail: videoDetails.thumbnails[0]?.url || '',
      author: videoDetails.author.name
    };
  } catch (error) {
    throw new Error(`Failed to get video info: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Downloads YouTube video and converts to MP3
 * @param url - YouTube video URL
 * @param outputDir - Directory to save the MP3 file
 * @param onProgress - Progress callback function
 * @returns Promise<string> - Path to the converted MP3 file
 */
export async function downloadAndConvertToMp3(
  url: string,
  outputDir: string,
  onProgress?: (progress: number, stage: string) => void
): Promise<string> {
  try {
    // Get video info first
    onProgress?.(10, 'Getting video information...');
    const videoInfo = await getVideoInfo(url);

    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Sanitize filename
    const sanitizedTitle = sanitizeFilename(videoInfo.title);
    const outputFilename = `${sanitizedTitle}.mp3`;
    const outputPath = path.join(outputDir, outputFilename);

    onProgress?.(20, 'Starting download...');

    return new Promise((resolve, reject) => {
      try {
        // Get audio stream
        const audioStream = ytdl(url, {
          quality: 'highestaudio',
          filter: 'audioonly',
        });

        onProgress?.(30, 'Downloading audio...');

        // Convert to MP3 using ffmpeg
        const ffmpegCommand = ffmpeg(audioStream)
          .audioBitrate(128)
          .audioCodec('libmp3lame')
          .format('mp3')
          .on('start', () => {
            onProgress?.(40, 'Converting to MP3...');
          })
          .on('progress', (progress) => {
            if (progress.percent) {
              const overallProgress = 40 + (progress.percent * 0.5);
              onProgress?.(Math.min(overallProgress, 90), 'Converting to MP3...');
            }
          })
          .on('end', () => {
            onProgress?.(100, 'Conversion complete!');
            resolve(outputPath);
          })
          .on('error', (error) => {
            reject(new Error(`FFmpeg error: ${error.message}`));
          });

        // Save to file
        ffmpegCommand.save(outputPath);

      } catch (error) {
        reject(new Error(`Download error: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    });

  } catch (error) {
    throw new Error(`Conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Formats duration from seconds to MM:SS format
 * @param seconds - Duration in seconds
 * @returns Formatted duration string
 */
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Validates if a YouTube URL is accessible
 * @param url - YouTube video URL
 * @returns Promise<boolean>
 */
export async function validateYouTubeUrl(url: string): Promise<boolean> {
  try {
    await ytdl.getInfo(url);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Gets the file size of a file
 * @param filePath - Path to the file
 * @returns Promise<number> - File size in bytes
 */
export async function getFileSize(filePath: string): Promise<number> {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}
