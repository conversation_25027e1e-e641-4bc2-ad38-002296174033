{"name": "miniget", "description": "A small HTTP(S) GET request library, with redirects and streaming.", "keywords": ["request", "http", "https", "redirect", "stream"], "version": "4.2.3", "repository": {"type": "git", "url": "git://github.com/fent/node-miniget.git"}, "author": "fent <<EMAIL>> (https://github.com/fent)", "main": "./dist/index.js", "files": ["dist"], "scripts": {"prepare": "tsc -p tsconfig.build.json", "build": "tsc -p tsconfig.build.json", "test": "nyc --extension .ts --reporter=lcov --reporter=text-summary npm run test:unit", "test:unit": "mocha -- --require ts-node/register test/*-test.ts", "lint": "eslint ./src ./test", "lint:fix": "eslint --fix ./src ./test"}, "devDependencies": {"@types/mocha": "^7.0.0", "@types/node": "^17.0.8", "@types/sinon": "^9.0.8", "@typescript-eslint/eslint-plugin": "^4.8.2", "@typescript-eslint/parser": "^4.8.2", "eslint": "^7.14.0", "longjohn": "^0.2.12", "mocha": "^7.0.1", "nock": "^13.0.4", "nyc": "^15.0.0", "sinon": "^9.2.0", "stream-equal": "^1.1.1", "ts-node": "^8.10.1", "typescript": "^3.9.7"}, "engines": {"node": ">=12"}, "license": "MIT"}