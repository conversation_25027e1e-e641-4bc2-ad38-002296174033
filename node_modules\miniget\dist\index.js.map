{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,gDAAuF;AAEvF,kDAA0B;AAC1B,mCAAgD;AAGhD,MAAM,QAAQ,GAIV,EAAE,OAAO,EAAE,cAAI,EAAE,QAAQ,EAAE,eAAK,EAAE,CAAC;AACvC,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/D,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAE7C,qEAAqE;AACrE,MAAM,aAAa,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC7F,MAAM,cAAc,GAAG,CAAC,SAAS,CAAC,CAAC;AAoCnC,OAAO,CAAC,YAAY,GAAG,MAAM,YAAa,SAAQ,KAAK;IAErD,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF,CAAC;AAEF,OAAO,CAAC,cAAc,GAAG;IACvB,YAAY,EAAE,EAAE;IAChB,UAAU,EAAE,CAAC;IACb,aAAa,EAAE,CAAC;IAChB,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;CAClC,CAAC;AAEF,SAAS,OAAO,CAAC,GAAiB,EAAE,UAA2B,EAAE;;IAC/D,MAAM,IAAI,GAA2B,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACxF,MAAM,MAAM,GAAG,IAAI,oBAAW,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAmB,CAAC;IACxF,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;IAC1C,IAAI,aAA4B,CAAC;IACjC,IAAI,cAAsC,CAAC;IAC3C,IAAI,mBAAqC,CAAC;IAC1C,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,YAA0B,CAAC;IAC/B,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,aAAqB,CAAC;IAC1B,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI,UAAU,GAAG,CAAC,EAAE,QAAgB,CAAC;IACrC,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,qCAAqC;IACrC,UAAI,IAAI,CAAC,OAAO,0CAAE,KAAK,EAAE;QACvB,IAAI,CAAC,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,EAAE;YACL,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAChC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;SAC/B;KACF;IAED,gCAAgC;IAChC,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YAC3B,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;SAC/D,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KAClB;IAED,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAAC,mBAAmB,IAAI,UAAU,GAAG,CAAC,CAAC;IACvE,MAAM,gBAAgB,GAAG,GAAG,EAAE,CAAC,CAAC,YAAY,IAAI,UAAU,KAAK,aAAa,CAAC;IAE7E,MAAM,SAAS,GAAG,CAAC,GAA0B,EAAE,EAAE;QAC/C,mBAAmB,GAAG,IAAI,CAAC;QAC3B,OAAO,GAAG,CAAC,CAAC;QACZ,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC3B,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,YAAY,GAAG,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,CAAC,GAA0B,EAAE,EAAE;QAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,gBAAgB,EAAE,IAAI,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;YACzF,SAAS,CAAC,GAAG,CAAC,CAAC;YACf,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAMF,MAAM,YAAY,GAAG,CAAC,YAA0B,EAAW,EAAE;QAC3D,IAAI,MAAM,CAAC,SAAS,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QACvC,IAAI,kBAAkB,EAAE,EAAE;YACxB,OAAO,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;SAChD;aAAM,IACL,CAAC,CAAC,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,KAAK,WAAW,CAAC;YAC/D,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE;YAC7B,IAAI,EAAE,GAAG,YAAY,CAAC,UAAU;gBAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzD,YAAY,GAAG,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,EAAgB,EAAE,MAAgB,EAAE,EAAE;QAC3D,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;YACxB,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;SAC/C;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,IAAI,MAAM,GAAmB,EAAE,EAAE,OAAO,CAAC;QACzC,IAAI;YACF,IAAI,MAAM,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC1D,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE;gBACzB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,IAAI,EAAE,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI;gBACnD,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,QAAQ,EAAE;gBACnB,MAAM,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;aACvD;YACD,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC7C;QAAC,OAAO,GAAG,EAAE;YACZ,qDAAqD;SACtD;QACD,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,YAAY,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC,CAAC;YACtE,OAAO;SACR;QAED,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC5B,IAAI,YAAY,IAAI,UAAU,GAAG,CAAC,EAAE;YAClC,IAAI,KAAK,GAAG,UAAU,GAAG,UAAU,CAAC;YACpC,IAAI,GAAG,GAAG,QAAQ,IAAI,EAAE,CAAC;YACzB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE;gBACjD,KAAK,EAAE,SAAS,KAAK,IAAI,GAAG,EAAE;aAC/B,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI;gBACF,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;aACjC;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBAC1B,OAAO;aACR;YACD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;gBAC9B,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAAC,OAAO,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,YAAY,CAAC,8CAA8C,CAAC,CAAC,CAAC;oBAC/F,OAAO;iBACR;aACF;SACF;QAED,MAAM,OAAO,GAAG,CAAC,GAAyB,EAAQ,EAAE;YAClD,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,aAAa,EAAE;gBAAE,OAAO;aAAE;YACzD,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;gBAC1B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;aAC3B;iBAAM;gBACL,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;aACvD;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,OAAO,EAAE,CAAC;YACV,YAAY,CAAC,EAAE,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACtD,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;YAC/C,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE;QACpD,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,GAAG,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAElE,MAAM,KAAK,GAAG,GAAG,EAAE;YACjB,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBAC5B,MAAM,CAAC,GAAG,EAAE,CAAC;aACd;QACH,CAAC,CAAC;QAEF,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAoB,EAAE,EAAE;YAC/D,4BAA4B;YAC5B,uBAAuB;YACvB,IAAI,MAAM,CAAC,SAAS,EAAE;gBAAE,OAAO;aAAE;YACjC,IAAI,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAoB,CAAC,EAAE;gBACrD,IAAI,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;oBACpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC;iBACtE;qBAAM;oBACL,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE;wBACxB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;qBAC5B;yBAAM;wBACL,IAAI,GAAG,GAAG,IAAI,OAAO,CAAC,YAAY,CAAC,6CAA6C,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;wBAC1B,OAAO,EAAE,CAAC;wBACV,OAAO;qBACR;oBACD,UAAU,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;oBAC/E,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;iBAC9B;gBACD,OAAO,EAAE,CAAC;gBACV,OAAO;gBAEP,2BAA2B;aAC5B;iBAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAoB,CAAC,EAAE;gBACzD,IAAI,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;oBAClF,IAAI,GAAG,GAAG,IAAI,OAAO,CAAC,YAAY,CAAC,gBAAgB,GAAG,CAAC,UAAU,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;oBACrF,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;iBAC3B;gBACD,OAAO,EAAE,CAAC;gBACV,OAAO;aACR;iBAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,EAAE;gBAC5E,IAAI,GAAG,GAAG,IAAI,OAAO,CAAC,YAAY,CAAC,gBAAgB,GAAG,CAAC,UAAU,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;gBACrF,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE;oBACzB,OAAO,CAAC,GAAG,CAAC,CAAC;iBACd;qBAAM;oBACL,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;iBAC3B;gBACD,OAAO,EAAE,CAAC;gBACV,OAAO;aACR;YAED,mBAAmB,GAAG,GAA2B,CAAC;YAClD,IAAI,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC1D,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;oBACrE,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,EAAE,EAAE;wBACN,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;wBACrD,mBAAmB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;qBAC1C;iBACF;aACF;YACD,IAAI,CAAC,aAAa,EAAE;gBAClB,aAAa,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBACjE,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,OAAO;oBACrD,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;aAC/C;YACD,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvB,mBAAmB,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACrC,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;YACzD,cAAc,GAAG,GAAG,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC7B,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACzB,aAAa,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAC1C,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QAC5C,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,aAAa,CAAC,GAAG,WAAW,CAAC,CAAC;SAC/B;QACD,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACtC,aAAa,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC,CAAC;IAEF,MAAM,CAAC,KAAK,GAAG,CAAC,GAAW,EAAE,EAAE;QAC7B,OAAO,CAAC,IAAI,CAAC,mFAAmF,CAAC,CAAC;QAClG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,IAAI,WAAW,GAAU,EAAE,CAAC;IAC5B,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,EAAE;QACpC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3B,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,CAAC,MAAM,EAAE;QACpC,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,OAAO,GAAG;QAC/B,YAAY,CAAC,YAAY,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACnC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,IAAI,aAAa,EAAE;YACjB,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC;SACxB;aAAM;YACL,WAAW,GAAG,IAAI,CAAC;SACpB;IACH,CAAC,CAAC;IAEF,MAAM,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAClD,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;QAC1C,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACtC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC7B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,iBAAS,OAAO,CAAC"}