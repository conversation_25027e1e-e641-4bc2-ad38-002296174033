interface PlatformInfo {
    name: string;
    url: string | (() => string);
}

const platforms: PlatformInfo[] = [
    {
        name: 'linux-arm',
        url: 'https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-armhf-static.tar.xz',
    },
    {
        name: 'linux-arm64',
        url: 'https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz',
    },
    {
        name: 'linux-ia32',
        url: 'https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-i686-static.tar.xz',
    },
    {
        name: 'linux-x64',
        url: 'https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz',
    },

    {
        name: 'darwin-x64',
        url: 'https://evermeet.cx/ffmpeg/ffmpeg-4.3.2.7z',
    },

    {
        name: 'win32-ia32',
        url: 'https://github.com/sudo-nautilus/FFmpeg-Builds-Win32/releases/download/autobuild-2021-03-23-06-25/ffmpeg-n4.3.2-160-gfbb9368226-win32-gpl-4.3.zip',
    },
    {
        name: 'win32-x64',
        url: 'https://github.com/BtbN/FFmpeg-Builds/releases/download/autobuild-2021-03-21-12-59/ffmpeg-n4.3.2-160-gfbb9368226-win64-gpl-4.3.zip',
    },
];
