/**
 * Validates if a given URL is a valid YouTube URL
 * @param url - The URL to validate
 * @returns boolean indicating if the URL is valid
 */
export function isValidYouTubeUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    // Check for various YouTube domains
    const validDomains = [
      'youtube.com',
      'www.youtube.com',
      'm.youtube.com',
      'youtu.be',
      'www.youtu.be'
    ];

    if (!validDomains.includes(hostname)) {
      return false;
    }

    // For youtu.be links, check if there's a video ID
    if (hostname === 'youtu.be' || hostname === 'www.youtu.be') {
      const videoId = urlObj.pathname.slice(1);
      return videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId);
    }

    // For youtube.com links, check for video parameter
    if (hostname.includes('youtube.com')) {
      const videoId = urlObj.searchParams.get('v');
      if (!videoId) {
        return false;
      }
      return videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId);
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Extracts video ID from a YouTube URL
 * @param url - The YouTube URL
 * @returns The video ID or null if not found
 */
export function extractVideoId(url: string): string | null {
  if (!isValidYouTubeUrl(url)) {
    return null;
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    if (hostname === 'youtu.be' || hostname === 'www.youtu.be') {
      return urlObj.pathname.slice(1);
    }

    if (hostname.includes('youtube.com')) {
      return urlObj.searchParams.get('v');
    }

    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Sanitizes a filename by removing invalid characters
 * @param filename - The filename to sanitize
 * @returns A sanitized filename
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .trim()
    .substring(0, 100); // Limit length
}
