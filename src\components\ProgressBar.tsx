'use client';

import { ConversionProgress } from '@/types';

interface ProgressBarProps {
  progress: ConversionProgress;
  className?: string;
}

export default function ProgressBar({ progress, className = '' }: ProgressBarProps) {
  const getStageColor = (stage: ConversionProgress['stage']) => {
    switch (stage) {
      case 'validating':
        return 'bg-blue-500';
      case 'downloading':
        return 'bg-yellow-500';
      case 'converting':
        return 'bg-purple-500';
      case 'complete':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStageText = (stage: ConversionProgress['stage']) => {
    switch (stage) {
      case 'validating':
        return 'Validating URL...';
      case 'downloading':
        return 'Downloading video...';
      case 'converting':
        return 'Converting to MP3...';
      case 'complete':
        return 'Conversion complete!';
      case 'error':
        return 'Error occurred';
      default:
        return 'Processing...';
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {getStageText(progress.stage)}
        </span>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {Math.round(progress.progress)}%
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
        <div
          className={`h-2.5 rounded-full transition-all duration-300 ease-out ${getStageColor(progress.stage)}`}
          style={{ width: `${Math.min(progress.progress, 100)}%` }}
        ></div>
      </div>
      
      {progress.message && (
        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
          {progress.message}
        </p>
      )}
    </div>
  );
}
