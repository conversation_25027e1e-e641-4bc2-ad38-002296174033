/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/convert/route";
exports.ids = ["app/api/convert/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconvert%2Froute&page=%2Fapi%2Fconvert%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconvert%2Froute.ts&appDir=D%3A%5CDEV%5Cprojects%5Ccld%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDEV%5Cprojects%5Ccld&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconvert%2Froute&page=%2Fapi%2Fconvert%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconvert%2Froute.ts&appDir=D%3A%5CDEV%5Cprojects%5Ccld%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDEV%5Cprojects%5Ccld&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_DEV_projects_cld_src_app_api_convert_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/convert/route.ts */ \"(rsc)/./src/app/api/convert/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/convert/route\",\n        pathname: \"/api/convert\",\n        filename: \"route\",\n        bundlePath: \"app/api/convert/route\"\n    },\n    resolvedPagePath: \"D:\\\\DEV\\\\projects\\\\cld\\\\src\\\\app\\\\api\\\\convert\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_DEV_projects_cld_src_app_api_convert_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconvert%2Froute&page=%2Fapi%2Fconvert%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconvert%2Froute.ts&appDir=D%3A%5CDEV%5Cprojects%5Ccld%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDEV%5Cprojects%5Ccld&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/convert/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/convert/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_youtube__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/youtube */ \"(rsc)/./src/lib/youtube.ts\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./src/lib/validation.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        // Validate input\n        if (!url || typeof url !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'URL is required',\n                error: 'INVALID_INPUT'\n            }, {\n                status: 400\n            });\n        }\n        // Validate YouTube URL format\n        if (!(0,_lib_validation__WEBPACK_IMPORTED_MODULE_4__.isValidYouTubeUrl)(url)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid YouTube URL format',\n                error: 'INVALID_URL'\n            }, {\n                status: 400\n            });\n        }\n        // Validate if URL is accessible\n        const isAccessible = await (0,_lib_youtube__WEBPACK_IMPORTED_MODULE_3__.validateYouTubeUrl)(url);\n        if (!isAccessible) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Video not found or not accessible',\n                error: 'VIDEO_NOT_ACCESSIBLE'\n            }, {\n                status: 404\n            });\n        }\n        // Get video info\n        let videoInfo;\n        try {\n            videoInfo = await (0,_lib_youtube__WEBPACK_IMPORTED_MODULE_3__.getVideoInfo)(url);\n        } catch (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Failed to get video information',\n                error: 'VIDEO_INFO_ERROR'\n            }, {\n                status: 500\n            });\n        }\n        // Create output directory\n        const outputDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'public', 'downloads');\n        await fs__WEBPACK_IMPORTED_MODULE_2__.promises.mkdir(outputDir, {\n            recursive: true\n        });\n        // Download and convert\n        try {\n            const outputPath = await (0,_lib_youtube__WEBPACK_IMPORTED_MODULE_3__.downloadAndConvertToMp3)(url, outputDir);\n            const filename = path__WEBPACK_IMPORTED_MODULE_1___default().basename(outputPath);\n            const downloadUrl = `/downloads/${filename}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Conversion completed successfully',\n                downloadUrl,\n                filename\n            });\n        } catch (error) {\n            console.error('Conversion error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Failed to convert video',\n                error: 'CONVERSION_ERROR'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Internal server error',\n            error: 'INTERNAL_ERROR'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'YouTube to MP3 Converter API',\n        endpoints: {\n            POST: '/api/convert - Convert YouTube video to MP3'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/convert/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validation.ts":
/*!*******************************!*\
  !*** ./src/lib/validation.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractVideoId: () => (/* binding */ extractVideoId),\n/* harmony export */   isValidYouTubeUrl: () => (/* binding */ isValidYouTubeUrl),\n/* harmony export */   sanitizeFilename: () => (/* binding */ sanitizeFilename)\n/* harmony export */ });\n/**\n * Validates if a given URL is a valid YouTube URL\n * @param url - The URL to validate\n * @returns boolean indicating if the URL is valid\n */ function isValidYouTubeUrl(url) {\n    if (!url || typeof url !== 'string') {\n        return false;\n    }\n    try {\n        const urlObj = new URL(url);\n        const hostname = urlObj.hostname.toLowerCase();\n        // Check for various YouTube domains\n        const validDomains = [\n            'youtube.com',\n            'www.youtube.com',\n            'm.youtube.com',\n            'youtu.be',\n            'www.youtu.be'\n        ];\n        if (!validDomains.includes(hostname)) {\n            return false;\n        }\n        // For youtu.be links, check if there's a video ID\n        if (hostname === 'youtu.be' || hostname === 'www.youtu.be') {\n            const videoId = urlObj.pathname.slice(1);\n            return videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId);\n        }\n        // For youtube.com links, check for video parameter\n        if (hostname.includes('youtube.com')) {\n            const videoId = urlObj.searchParams.get('v');\n            if (!videoId) {\n                return false;\n            }\n            return videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId);\n        }\n        return false;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Extracts video ID from a YouTube URL\n * @param url - The YouTube URL\n * @returns The video ID or null if not found\n */ function extractVideoId(url) {\n    if (!isValidYouTubeUrl(url)) {\n        return null;\n    }\n    try {\n        const urlObj = new URL(url);\n        const hostname = urlObj.hostname.toLowerCase();\n        if (hostname === 'youtu.be' || hostname === 'www.youtu.be') {\n            return urlObj.pathname.slice(1);\n        }\n        if (hostname.includes('youtube.com')) {\n            return urlObj.searchParams.get('v');\n        }\n        return null;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Sanitizes a filename by removing invalid characters\n * @param filename - The filename to sanitize\n * @returns A sanitized filename\n */ function sanitizeFilename(filename) {\n    return filename.replace(/[<>:\"/\\\\|?*]/g, '') // Remove invalid characters\n    .replace(/\\s+/g, '_') // Replace spaces with underscores\n    .replace(/_{2,}/g, '_') // Replace multiple underscores with single\n    .trim().substring(0, 100); // Limit length\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/youtube.ts":
/*!****************************!*\
  !*** ./src/lib/youtube.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downloadAndConvertToMp3: () => (/* binding */ downloadAndConvertToMp3),\n/* harmony export */   getFileSize: () => (/* binding */ getFileSize),\n/* harmony export */   getVideoInfo: () => (/* binding */ getVideoInfo),\n/* harmony export */   validateYouTubeUrl: () => (/* binding */ validateYouTubeUrl)\n/* harmony export */ });\n/* harmony import */ var ytdl_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ytdl-core */ \"ytdl-core\");\n/* harmony import */ var ytdl_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(ytdl_core__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fluent_ffmpeg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fluent-ffmpeg */ \"fluent-ffmpeg\");\n/* harmony import */ var fluent_ffmpeg__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fluent_ffmpeg__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _validation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validation */ \"(rsc)/./src/lib/validation.ts\");\n\n\n\n\n\n// Try to set ffmpeg path, but handle errors gracefully\ntry {\n    const ffmpegPath = (__webpack_require__(/*! @ffmpeg-installer/ffmpeg */ \"@ffmpeg-installer/ffmpeg\").path);\n    if (ffmpegPath) {\n        fluent_ffmpeg__WEBPACK_IMPORTED_MODULE_1___default().setFfmpegPath(ffmpegPath);\n        console.log('FFmpeg path set to:', ffmpegPath);\n    }\n} catch (error) {\n    console.warn('FFmpeg installer not available, using system ffmpeg if available:', error);\n}\n/**\n * Gets video information from YouTube URL\n * @param url - YouTube video URL\n * @returns Promise<VideoInfo>\n */ async function getVideoInfo(url) {\n    try {\n        const info = await ytdl_core__WEBPACK_IMPORTED_MODULE_0___default().getInfo(url);\n        const videoDetails = info.videoDetails;\n        return {\n            title: videoDetails.title,\n            duration: formatDuration(parseInt(videoDetails.lengthSeconds)),\n            thumbnail: videoDetails.thumbnails[0]?.url || '',\n            author: videoDetails.author.name\n        };\n    } catch (error) {\n        throw new Error(`Failed to get video info: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Downloads YouTube video and converts to MP3\n * @param url - YouTube video URL\n * @param outputDir - Directory to save the MP3 file\n * @param onProgress - Progress callback function\n * @returns Promise<string> - Path to the converted MP3 file\n */ async function downloadAndConvertToMp3(url, outputDir, onProgress) {\n    try {\n        // Get video info first\n        onProgress?.(10, 'Getting video information...');\n        const videoInfo = await getVideoInfo(url);\n        // Create output directory if it doesn't exist\n        await fs__WEBPACK_IMPORTED_MODULE_2__.promises.mkdir(outputDir, {\n            recursive: true\n        });\n        // Sanitize filename\n        const sanitizedTitle = (0,_validation__WEBPACK_IMPORTED_MODULE_4__.sanitizeFilename)(videoInfo.title);\n        const outputFilename = `${sanitizedTitle}.mp3`;\n        const outputPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(outputDir, outputFilename);\n        onProgress?.(20, 'Starting download...');\n        return new Promise((resolve, reject)=>{\n            try {\n                // Get audio stream\n                const audioStream = ytdl_core__WEBPACK_IMPORTED_MODULE_0___default()(url, {\n                    quality: 'highestaudio',\n                    filter: 'audioonly'\n                });\n                onProgress?.(30, 'Downloading audio...');\n                // Convert to MP3 using ffmpeg\n                const ffmpegCommand = fluent_ffmpeg__WEBPACK_IMPORTED_MODULE_1___default()(audioStream).audioBitrate(128).audioCodec('libmp3lame').format('mp3').on('start', ()=>{\n                    onProgress?.(40, 'Converting to MP3...');\n                }).on('progress', (progress)=>{\n                    if (progress.percent) {\n                        const overallProgress = 40 + progress.percent * 0.5;\n                        onProgress?.(Math.min(overallProgress, 90), 'Converting to MP3...');\n                    }\n                }).on('end', ()=>{\n                    onProgress?.(100, 'Conversion complete!');\n                    resolve(outputPath);\n                }).on('error', (error)=>{\n                    reject(new Error(`FFmpeg error: ${error.message}`));\n                });\n                // Save to file\n                ffmpegCommand.save(outputPath);\n            } catch (error) {\n                reject(new Error(`Download error: ${error instanceof Error ? error.message : 'Unknown error'}`));\n            }\n        });\n    } catch (error) {\n        throw new Error(`Conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Formats duration from seconds to MM:SS format\n * @param seconds - Duration in seconds\n * @returns Formatted duration string\n */ function formatDuration(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n/**\n * Validates if a YouTube URL is accessible\n * @param url - YouTube video URL\n * @returns Promise<boolean>\n */ async function validateYouTubeUrl(url) {\n    try {\n        await ytdl_core__WEBPACK_IMPORTED_MODULE_0___default().getInfo(url);\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Gets the file size of a file\n * @param filePath - Path to the file\n * @returns Promise<number> - File size in bytes\n */ async function getFileSize(filePath) {\n    try {\n        const stats = await fs__WEBPACK_IMPORTED_MODULE_2__.promises.stat(filePath);\n        return stats.size;\n    } catch (error) {\n        return 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/youtube.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@ffmpeg-installer/ffmpeg":
/*!*******************************************!*\
  !*** external "@ffmpeg-installer/ffmpeg" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@ffmpeg-installer/ffmpeg");

/***/ }),

/***/ "fluent-ffmpeg":
/*!********************************!*\
  !*** external "fluent-ffmpeg" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("fluent-ffmpeg");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "ytdl-core":
/*!****************************!*\
  !*** external "ytdl-core" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("ytdl-core");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconvert%2Froute&page=%2Fapi%2Fconvert%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconvert%2Froute.ts&appDir=D%3A%5CDEV%5Cprojects%5Ccld%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDEV%5Cprojects%5Ccld&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();