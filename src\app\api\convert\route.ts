import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import { promises as fs } from 'fs';
import { downloadAndConvertToMp3, getVideoInfo, validateYouTubeUrl } from '@/lib/youtube';
import { isValidYouTubeUrl } from '@/lib/validation';
import { ConversionRequest, ConversionResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: ConversionRequest = await request.json();
    const { url } = body;

    // Validate input
    if (!url || typeof url !== 'string') {
      return NextResponse.json<ConversionResponse>({
        success: false,
        message: 'URL is required',
        error: 'INVALID_INPUT'
      }, { status: 400 });
    }

    // Validate YouTube URL format
    if (!isValidYouTubeUrl(url)) {
      return NextResponse.json<ConversionResponse>({
        success: false,
        message: 'Invalid YouTube URL format',
        error: 'INVALID_URL'
      }, { status: 400 });
    }

    // Validate if URL is accessible
    const isAccessible = await validateYouTubeUrl(url);
    if (!isAccessible) {
      return NextResponse.json<ConversionResponse>({
        success: false,
        message: 'Video not found or not accessible',
        error: 'VIDEO_NOT_ACCESSIBLE'
      }, { status: 404 });
    }

    // Get video info
    let videoInfo;
    try {
      videoInfo = await getVideoInfo(url);
    } catch (error) {
      return NextResponse.json<ConversionResponse>({
        success: false,
        message: 'Failed to get video information',
        error: 'VIDEO_INFO_ERROR'
      }, { status: 500 });
    }

    // Create output directory
    const outputDir = path.join(process.cwd(), 'public', 'downloads');
    await fs.mkdir(outputDir, { recursive: true });

    // Download and convert
    try {
      const outputPath = await downloadAndConvertToMp3(url, outputDir);
      const filename = path.basename(outputPath);
      const downloadUrl = `/downloads/${filename}`;

      return NextResponse.json<ConversionResponse>({
        success: true,
        message: 'Conversion completed successfully',
        downloadUrl,
        filename
      });

    } catch (error) {
      console.error('Conversion error:', error);
      return NextResponse.json<ConversionResponse>({
        success: false,
        message: 'Failed to convert video',
        error: 'CONVERSION_ERROR'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json<ConversionResponse>({
      success: false,
      message: 'Internal server error',
      error: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'YouTube to MP3 Converter API',
    endpoints: {
      POST: '/api/convert - Convert YouTube video to MP3'
    }
  });
}
