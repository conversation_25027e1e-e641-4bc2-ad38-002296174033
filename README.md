# YouTube to MP3 Converter

A modern, fast, and secure YouTube to MP3 converter built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

- 🚀 **Fast Conversion**: Quick and efficient conversion process with real-time progress tracking
- 🎵 **High Quality**: Extract audio in high quality MP3 format with optimal bitrate (128kbps)
- 🔒 **Secure & Private**: Your data is processed securely and files are automatically cleaned up
- 📱 **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- 🌙 **Dark Mode Support**: Beautiful interface with automatic dark/light mode detection
- ⚡ **Real-time Progress**: Live progress updates during conversion process
- 🛡️ **Error Handling**: Comprehensive error handling with user-friendly messages
- 🎯 **URL Validation**: Smart YouTube URL validation and video ID extraction

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Audio Processing**: ytdl-core + fluent-ffmpeg
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd youtube-to-mp3-converter
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Copy YouTube URL**: Copy the URL of the YouTube video you want to convert
2. **Paste URL**: Paste the URL in the input field on the homepage
3. **Convert**: Click "Convert to MP3" and wait for the conversion to complete
4. **Download**: Download your MP3 file when ready

### Supported URL Formats

- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://m.youtube.com/watch?v=VIDEO_ID`

## API Endpoints

### GET /api/convert
Returns API information and available endpoints.

### POST /api/convert
Converts a YouTube video to MP3.

**Request Body:**
```json
{
  "url": "https://www.youtube.com/watch?v=VIDEO_ID"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Conversion completed successfully",
  "downloadUrl": "/downloads/filename.mp3",
  "filename": "filename.mp3"
}
```

## Project Structure

```
src/
├── app/
│   ├── api/convert/route.ts    # API endpoint for conversion
│   ├── layout.tsx              # Root layout with metadata
│   ├── page.tsx                # Homepage
│   └── globals.css             # Global styles
├── components/
│   ├── YouTubeConverter.tsx    # Main converter component
│   ├── ProgressBar.tsx         # Progress indicator
│   └── ErrorBoundary.tsx       # Error boundary component
├── lib/
│   ├── youtube.ts              # YouTube processing utilities
│   └── validation.ts           # URL validation utilities
└── types/
    └── index.ts                # TypeScript type definitions
```

## Environment Variables

No environment variables are required for basic functionality. The application works out of the box.

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms

The application can be deployed to any platform that supports Node.js applications:

- Netlify
- Railway
- Heroku
- DigitalOcean App Platform

## Legal Notice

⚠️ **Important**: Please respect copyright laws and only convert videos you have permission to use. This tool is for educational and personal use only.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter any issues or have questions, please open an issue on GitHub.
